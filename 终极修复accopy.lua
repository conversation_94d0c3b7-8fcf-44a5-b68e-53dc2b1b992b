-- 终极修复 accopy:-1 错误
-- 这个脚本会完全阻止 accopy 链接类型的处理，防止错误发生

local function UltimateFixAccopy()
    print("|cffff0000=== 终极修复 accopy 错误 ===|r")
    
    -- 1. 立即停止所有时间戳功能
    SetCVar("showTimestamps", "none")
    _G.CHAT_TIMESTAMP_FORMAT = nil
    
    -- 2. Hook ItemRefTooltip.SetHyperlink 来阻止 accopy 链接
    if ItemRefTooltip and ItemRefTooltip.SetHyperlink then
        local originalSetHyperlink = ItemRefTooltip.SetHyperlink
        ItemRefTooltip.SetHyperlink = function(self, link, ...)
            -- 如果是 accopy 链接，直接忽略
            if link and link:find("accopy") then
                print("|cffff8000阻止了 accopy 链接: " .. link .. "|r")
                return
            end
            -- 否则调用原始函数
            return originalSetHyperlink(self, link, ...)
        end
        print("✓ 已Hook ItemRefTooltip.SetHyperlink 阻止 accopy 链接")
    end
    
    -- 3. Hook ChatFrame_OnHyperlinkShow 来阻止 accopy 链接
    if ChatFrame_OnHyperlinkShow then
        local originalChatFrameOnHyperlinkShow = ChatFrame_OnHyperlinkShow
        ChatFrame_OnHyperlinkShow = function(chatFrame, link, text, button)
            -- 如果是 accopy 链接，直接忽略
            if link and link:find("accopy") then
                print("|cffff8000阻止了 ChatFrame accopy 链接: " .. link .. "|r")
                return
            end
            -- 否则调用原始函数
            return originalChatFrameOnHyperlinkShow(chatFrame, link, text, button)
        end
        print("✓ 已Hook ChatFrame_OnHyperlinkShow 阻止 accopy 链接")
    end
    
    -- 4. Hook SetItemRef 来阻止 accopy 链接
    if SetItemRef then
        local originalSetItemRef = SetItemRef
        SetItemRef = function(link, text, button, chatFrame)
            -- 如果是 accopy 链接，直接忽略
            if link and link:find("accopy") then
                print("|cffff8000阻止了 SetItemRef accopy 链接: " .. link .. "|r")
                return
            end
            -- 否则调用原始函数
            return originalSetItemRef(link, text, button, chatFrame)
        end
        print("✓ 已Hook SetItemRef 阻止 accopy 链接")
    end
    
    -- 5. 清理任何可能的 accopy 相关配置
    if WanTinyDB and WanTinyDB.WChat then
        local config = WanTinyDB.WChat
        for key, value in pairs(config) do
            if type(value) == "string" and value:find("accopy") then
                config[key] = nil
                print("✓ 清理了配置中的 accopy: " .. key)
            end
        end
    end
    
    -- 6. 检查并清理 CVar
    local currentTimestamp = GetCVar("showTimestamps")
    if currentTimestamp and currentTimestamp:find("accopy") then
        SetCVar("showTimestamps", "none")
        print("✓ 清理了 CVar 中的 accopy 格式")
    end
    
    print("|cff00ff00✓ 终极修复完成！accopy 错误应该已被完全阻止|r")
    print("|cffff8000现在可以安全使用聊天功能，不会再出现 accopy 错误|r")
end

-- 安全启用简单时间戳的函数（不使用超链接）
local function EnableSafeTimestamp()
    print("|cff00ff00=== 启用安全时间戳 ===|r")
    
    -- 使用简单的时间戳格式，不包含任何超链接
    local safeFormat = "[%H:%M:%S]"
    SetCVar("showTimestamps", safeFormat)
    _G.CHAT_TIMESTAMP_FORMAT = safeFormat
    
    print("✓ 已启用安全的时间戳格式: " .. safeFormat)
    print("|cffff8000注意: 此格式不支持点击复制，但不会产生错误|r")
end

-- 完全禁用时间戳的函数
local function DisableAllTimestamps()
    print("|cffff0000=== 完全禁用时间戳 ===|r")
    
    SetCVar("showTimestamps", "none")
    _G.CHAT_TIMESTAMP_FORMAT = nil
    
    if WChat_Config then
        WChat_Config.EnableTimestamp = false
    end
    
    print("✓ 所有时间戳功能已禁用")
end

-- 检查当前状态
local function CheckStatus()
    print("|cffffe00a=== 当前状态检查 ===|r")
    
    local currentTimestamp = GetCVar("showTimestamps")
    print("当前时间戳: " .. (currentTimestamp or "none"))
    
    if currentTimestamp and currentTimestamp:find("accopy") then
        print("|cffff0000⚠️  仍然包含 accopy！|r")
        print("|cffff8000建议运行: UltimateFixAccopy()|r")
    else
        print("|cff00ff00✓ 没有发现 accopy|r")
    end
    
    -- 检查Hook状态
    print("Hook状态:")
    print("  ItemRefTooltip.SetHyperlink: " .. (ItemRefTooltip.SetHyperlink and "已Hook" or "未Hook"))
    print("  ChatFrame_OnHyperlinkShow: " .. (ChatFrame_OnHyperlinkShow and "已Hook" or "未Hook"))
    print("  SetItemRef: " .. (SetItemRef and "已Hook" or "未Hook"))
end

-- 暴露函数到全局
_G.UltimateFixAccopy = UltimateFixAccopy
_G.EnableSafeTimestamp = EnableSafeTimestamp
_G.DisableAllTimestamps = DisableAllTimestamps
_G.CheckAccopyStatus = CheckStatus

-- 立即运行终极修复
print("|cffff0000立即运行终极修复以阻止 accopy 错误...|r")
UltimateFixAccopy()

print("\n|cff00ff00可用命令:|r")
print("|cffff8000/script UltimateFixAccopy()|r - 终极修复accopy错误")
print("|cffff8000/script EnableSafeTimestamp()|r - 启用安全时间戳（无超链接）")
print("|cffff8000/script DisableAllTimestamps()|r - 完全禁用时间戳")
print("|cffff8000/script CheckAccopyStatus()|r - 检查当前状态")

-- 额外的保护：定期检查并清理 accopy
local function PeriodicCleanup()
    local currentTimestamp = GetCVar("showTimestamps")
    if currentTimestamp and currentTimestamp:find("accopy") then
        SetCVar("showTimestamps", "none")
        print("|cffff8000自动清理了 accopy 时间戳格式|r")
    end
end

-- 每5秒检查一次
local cleanupTimer = C_Timer.NewTicker(5, PeriodicCleanup)
print("|cff00ff00已启动自动清理程序，每5秒检查一次 accopy 格式|r")
