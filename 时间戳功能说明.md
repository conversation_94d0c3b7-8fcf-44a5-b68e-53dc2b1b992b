# WChat 时间戳功能说明

## 功能概述

WChat 现在支持自定义时间戳颜色和点击时间复制消息功能，参考了 ALAchat 的实现方式。

## 主要功能

### 1. 自定义时间戳颜色
- 可以设置时间戳的显示颜色
- 支持RGB颜色选择器
- 实时预览颜色效果

### 2. 点击时间戳复制消息
- 点击聊天消息前的时间戳
- 自动将整条消息复制到聊天输入框
- 自动清理富文本标记，保留纯文本内容

### 3. 多种时间戳格式
- `[%H:%M:%S]` - [06:38:35] (默认)
- `%H:%M:%S` - 06:38:35
- `[%H:%M]` - [06:38]
- `%H:%M` - 06:38
- `[%I:%M:%S %p]` - [06:38:35 AM]
- `%I:%M:%S %p` - 06:38:35 AM

## 使用方法

### 通过配置界面
1. 打开 WanTiny 配置界面 (`/wantiny`)
2. 切换到 "WChat" 标签页
3. 在聊天增强设置区域：
   - 勾选 "启用时间戳" 复选框
   - 点击 "时间颜色" 按钮选择颜色
   - 使用 "时间显示格式" 下拉菜单选择格式

### 通过命令行
```lua
-- 启用时间戳功能
/script WChat.ToggleTimestamp(true)

-- 设置时间戳颜色 (RGB值 0-1)
/script WChat.OnConfigChanged.TimestampColor({1, 0, 0}) -- 红色

-- 设置时间戳格式
/script WChat.SetTimestampFormat("[%H:%M]")

-- 打开颜色选择器
/script WChat.ShowTimestampColorPicker()
```

## 技术实现

### 核心原理
1. **超链接包装**: 将时间戳包装为可点击的超链接格式
2. **颜色编码**: 使用 `|cffRRGGBB` 格式设置时间戳颜色
3. **事件处理**: Hook `ItemRefTooltip.SetHyperlink` 处理点击事件
4. **文本清理**: 自动移除富文本标记，保留纯文本内容

### 格式字符串
```lua
-- 最终的时间戳格式
"|cffRRGGBB|Hwchat_timestamp:-1|h[%H:%M:%S]|h|r"
```

### 配置存储
时间戳配置存储在 `WanTinyDB.WChat` 中：
```lua
{
    EnableTimestamp = true,
    TimestampColor = {1.0, 0.5, 0.0}, -- RGB值
    TimestampFormat = "[%H:%M:%S]"
}
```

## 注意事项

1. **兼容性**: 与其他聊天插件可能存在冲突，建议单独使用
2. **性能**: 时间戳功能对性能影响很小
3. **保存**: 配置会自动保存到角色数据中
4. **重载**: 修改配置后立即生效，无需重载界面

## 故障排除

### 时间戳不显示
1. 检查是否启用了时间戳功能
2. 确认 WChat 模块已正确加载
3. 尝试重新设置时间戳格式

### 点击无反应
1. 确保点击的是时间戳部分（有颜色的时间）
2. 检查聊天输入框是否可用
3. 尝试重新启用时间戳功能

### 颜色不正确
1. 重新打开颜色选择器设置
2. 检查RGB值是否在0-1范围内
3. 确认时间戳功能已启用

## 更新日志

### v1.0
- 添加基础时间戳功能
- 支持自定义颜色和格式
- 实现点击复制消息功能
- 集成到 WanTinyUI 配置界面
