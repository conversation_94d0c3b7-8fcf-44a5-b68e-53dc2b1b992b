-- WChat 聊天增强模块 - 整合版本，移除ACE3依赖
-- 注册到WanTiny模块系统
WanTiny_RegisterModule("WChat", function()

-- 模块对象
local WChat = {}

-- 默认配置
local defaultConfig = {
    UseTopInput = false,
    UseVertical = false,
    EnableChannelShortNames = false,
    LockChatBar = false,
    EnableEmoteInput = true,
    EmoteIconSize = 16,
    EmoteIconListSize = 20,
    EmoteOffsetX = 30,
    EmoteOffsetY = 30,
    DistanceHorizontal = 25,
    DistanceVertical = 25,
    AlphaOnLeave = 0.5,
    AlphaOnEnter = 1.0,
    Position = nil,
    -- 时间戳相关配置
    EnableTimestamp = false,
    TimestampColor = {1.0, 0.5, 0.0}, -- 橙色
    TimestampFormat = "[%H:%M:%S]"
}

-- 获取配置
local function GetWChatConfig()
    _G.WanTinyDB = _G.WanTinyDB or {}
    _G.WanTinyDB.WChat = _G.WanTinyDB.WChat or {}

    -- 合并默认配置
    for k, v in pairs(defaultConfig) do
        if _G.WanTinyDB.WChat[k] == nil then
            _G.WanTinyDB.WChat[k] = v
        end
    end

    return _G.WanTinyDB.WChat
end

-- 密语记录数据库
local function GetWhisperDB()
    _G.WanTinyDB = _G.WanTinyDB or {}
    _G.WanTinyDB.WhisperRecord = _G.WanTinyDB.WhisperRecord or {record = {}}
    return _G.WanTinyDB.WhisperRecord
end

-- 常量定义
local CONSTANTS = {
    COLORS = {
        BORDER = {0.3, 0.3, 0.3, 1},
        MOVELOCK = {0.20, 0.20, 0.80},
        SAY = {1.00, 1.00, 1.00},
        YELL = {1.00, 0.25, 0.25},
        PARTY = {0.66, 0.66, 1.00},
        GUILD = {0.25, 1.00, 0.25},
        RAID = {1.00, 0.50, 0.00},
        CHANNEL = {0.82, 0.70, 0.55},
        WORLD = {0.78, 1.00, 0.59},
        WHISPER = {1.00, 0.50, 1.00},
        ROLL = {1.00, 1.00, 0.00},
        GOLD = {1.00, 0.84, 0.00},
        AI = {0.20, 0.80, 0.80},
        ATLAS = {0.20, 0.80, 0.20},
        CHATCOPY = {0.20, 0.60, 0.80}
    },
    BUTTON = {
        SIZE = 22,
        FONT_SIZE = 15,
        TEXT_WIDTH = 26,
        TEXT_HEIGHT = 26
    },
    FLASH = {
        INTERVAL = 0.6
    },
    TOOLTIPS = {
        chnGen = "|cff00ffff鼠标左键|r-|cffff80ff加入/发言|r\n|cff00ffff鼠标右键|r-|cffff80ff切换频道消息显示/屏蔽|r",
        chnTrade = "|cff00ffff鼠标左键|r-|cffff80ff加入/发言|r\n|cff00ffff鼠标右键|r-|cffff80ff切换频道消息显示/屏蔽|r",
        chnLFG = "|cff00ffff鼠标左键|r-|cffff80ff加入/发言|r\n|cff00ffff鼠标右键|r-|cffff80ff切换频道消息显示/屏蔽|r",
        world = "|cff00ffff鼠标左键|r-|cffff80ff加入/发言|r\n|cff00ffff鼠标右键|r-|cffff80ff切换频道消息显示/屏蔽|r",
        roll = "|cff00ffff鼠标左键|r-|cffff80ff随机Roll点|r\n|cff00ffff鼠标右键|r-|cffff80ff打开战利品投骰界面|r",
        gold = "|cff00ffff鼠标左键|r-|cffff80ff打开金团表|r",
        ai = "|cff00ffff鼠标左键|r-|cffff80ff打开AI助手|r",
        atlas = "|cff00ffff鼠标左键|r-|cffff80ff打开副本掉落/AtlasLoot|r",
        chatcopy = "|cff00ffff鼠标左键|r-|cffff80ff聊天内容复制|r",
        minimize = "|cff00ffff鼠标左键|r-|cffff80ff聊天窗口最小化/还原|r",
        whisper = "|cff00ffff鼠标左键|r-|cffff80ff密语记录/未读高亮|r"
    }
}

-- 变量定义
local WChat_Config
local IsMovable = false
local ChatBar = CreateFrame("Frame", nil, UIParent, "BackdropTemplate")
WChatBar = ChatBar

-- 工具函数
local function PrintMessage(message, color)
    color = color or "00d200"
    print("|cffffe00a<|r|cffff7d0aWChat|r|cffffe00a>|r |cff" .. color .. message .. "|r")
end

local function ToggleJoinChannelByName(channelName)
    local chnId = GetChannelName(channelName)
    if chnId == 0 then
        JoinPermanentChannel(channelName)
        ChatFrame_AddChannel(DEFAULT_CHAT_FRAME, channelName)
        PrintMessage("加入" .. channelName .. "频道")
    else
        LeaveChannelByName(channelName)
        PrintMessage("离开" .. channelName .. "频道", "d20000")
    end
end

local function JoinAndInputChannelByName(channelName)
    local chnId = GetChannelName(channelName)
    if chnId == 0 then
        JoinPermanentChannel(channelName)
        ChatFrame_AddChannel(DEFAULT_CHAT_FRAME, channelName)
        PrintMessage("已加入" .. channelName .. "频道，准备发言")
        C_Timer.After(0.2, function()
            local newId = GetChannelName(channelName)
            if newId > 0 then
                ChatFrame_OpenChat("/" .. newId .. " " .. DEFAULT_CHAT_FRAME.editBox:GetText(), DEFAULT_CHAT_FRAME)
            end
        end)
    else
        ChatFrame_OpenChat("/" .. chnId .. " " .. DEFAULT_CHAT_FRAME.editBox:GetText(), DEFAULT_CHAT_FRAME)
    end
end

local function OpenChatWithCommand(command)
    ChatFrame_OpenChat(command .. " " .. DEFAULT_CHAT_FRAME.editBox:GetText(), DEFAULT_CHAT_FRAME)
end

-- 判断频道是否已在主聊天框显示
local function IsChannelShown(channelName)
    local channels = {GetChatWindowChannels(DEFAULT_CHAT_FRAME:GetID() or 1)}
    for i = 1, #channels, 2 do
        if channels[i] == channelName then
            return true
        end
    end
    return false
end

-- 切换频道消息显示/屏蔽
local function ToggleChannelShowHide(channelName)
    if IsChannelShown(channelName) then
        ChatFrame_RemoveChannel(DEFAULT_CHAT_FRAME, channelName)
        PrintMessage("已屏蔽频道："..channelName, "d20000")
    else
        ChatFrame_AddChannel(DEFAULT_CHAT_FRAME, channelName)
        PrintMessage("已恢复频道："..channelName)
    end
end

-- 频道映射配置
local CHANNEL_MAPPINGS = {
    ["chnGen"] = "综合",
    ["chnTrade"] = "交易",
    ["chnLFG"] = "寻求组队",
    ["world"] = "大脚世界频道"
}

-- 更新频道按钮X图标状态
local function UpdateChannelXIcons()
    if not ChatBar then return end

    local children = {ChatBar:GetChildren()}
    for _, child in pairs(children) do
        if child.X and child.buttonName and CHANNEL_MAPPINGS[child.buttonName] then
            local channelName = CHANNEL_MAPPINGS[child.buttonName]
            child.X:SetShown(not IsChannelShown(channelName))
        end
    end
end

-- 基础聊天频道点击函数
local function ChannelSay_OnClick()
    OpenChatWithCommand("/s")
end

local function ChannelYell_OnClick()
    OpenChatWithCommand("/y")
end

local function ChannelParty_OnClick()
    OpenChatWithCommand("/p")
end

local function ChannelGuild_OnClick()
    OpenChatWithCommand("/g")
end

local function ChannelRaid_OnClick()
    OpenChatWithCommand("/raid")
end

local function ChannelBG_OnClick()
    if not UnitInBattleground("player") then
        PrintMessage("你没有处于战场")
        return
    end
    OpenChatWithCommand("/bg")
end

-- 频道按钮点击处理
local function ChannelGen_OnClick(self, button)
    if button == "LeftButton" then
        JoinAndInputChannelByName("综合")
    else
        ToggleChannelShowHide("综合")
        C_Timer.After(0.1, UpdateChannelXIcons)
    end
end

local function ChannelTrade_OnClick(self, button)
    if button == "LeftButton" then
        JoinAndInputChannelByName("交易")
    else
        ToggleChannelShowHide("交易")
        C_Timer.After(0.1, UpdateChannelXIcons)
    end
end

local function ChannelLFG_OnClick(self, button)
    if button == "LeftButton" then
        JoinAndInputChannelByName("寻求组队")
    else
        ToggleChannelShowHide("寻求组队")
        C_Timer.After(0.1, UpdateChannelXIcons)
    end
end

local function ChannelWorld_OnClick(self, button)
    if button == "LeftButton" then
        JoinAndInputChannelByName("大脚世界频道")
    else
        ToggleChannelShowHide("大脚世界频道")
        C_Timer.After(0.1, UpdateChannelXIcons)
    end
end

local function ChatEmote_OnClick()
    WChat.ToggleEmoteTable()
end

local function Roll_OnClick(self, button)
    if button == "RightButton" then
        ToggleLootHistoryFrame()
    else
        RandomRoll(1, 100)
    end
end

local function ChatCopy_OnClick()
    WChat.CopyFunc()
end

local function SaveChatBarPosition()
    local point, relativeTo, relativePoint, xOfs, yOfs = ChatBar:GetPoint()
    WChat_Config.Position = {point = point, relativeTo = relativeTo and relativeTo:GetName() or nil, relativePoint = relativePoint, xOfs = xOfs, yOfs = yOfs}
end

local function SetChatBarBackdrop(enabled)
    ChatBar:SetBackdrop(enabled and {bgFile = "Interface/DialogFrame/UI-DialogBox-Background", edgeFile = "Interface/DialogFrame/UI-DialogBox-Border", tile = true, tileSize = 16, edgeSize = 16, insets = {left = 4, right = 4, top = 4, bottom = 4}} or {})
end

local function Movelock_OnClick(self, button)
    if button == "LeftButton" then
        IsMovable = not IsMovable
        PrintMessage(IsMovable and "解锁聊天条" or "锁定聊天条", IsMovable and nil or "d20000")
        SetChatBarBackdrop(IsMovable)
        if not IsMovable then
            SaveChatBarPosition()
        end
        ChatBar:EnableMouse(IsMovable)
    elseif button == "MiddleButton" and IsMovable then
        ChatBar:ClearAllPoints()
        ChatBar:SetPoint("BOTTOMLEFT", UIParent, "BOTTOMLEFT", 0, 0)
    end
end

local BGButtonFrame -- 保存"战/副"按钮frame

local function UpdateBGButtonText()
    if not BGButtonFrame or not BGButtonFrame.text then return end
    local inBG = UnitInBattleground("player")
    local inInstance, instanceType = IsInInstance()
    if inBG then
        BGButtonFrame.text:SetText("战")
    elseif inInstance and (instanceType == "party" or instanceType == "raid") then
        BGButtonFrame.text:SetText("副")
    else
        BGButtonFrame.text:SetText("副")
    end
end

local function ChannelBGOrLFG_OnClick(self, button)
    local inBG = UnitInBattleground("player")
    local inInstance, instanceType = IsInInstance()
    
    if inBG then
        OpenChatWithCommand("/bg")
    elseif inInstance and (instanceType == "party" or instanceType == "raid") then
        OpenChatWithCommand("/i")
    else
        PrintMessage("你不在战场或副本中", "d20000")
    end
end

-- 聊天条“最小化”按钮，支持状态切换“隐/显”
local function UpdateMinimizeButtonText()
    if not WChatBar or not WChatBar.MinimizeBtn then return end
    local btn = _G.WChat_ChatHideButton
    local shown = _G.ChatFrame1 and _G.ChatFrame1:IsVisible()
    if shown then
        WChatBar.MinimizeBtn.text:SetText("隐")
    else
        WChatBar.MinimizeBtn.text:SetText("显")
    end
end

local function ChatMinimize_OnClick(self, button)
    local btn = _G.WChat_ChatHideButton
    if btn and btn:GetScript("OnClick") then
        btn:Click()
        C_Timer.After(0.05, UpdateMinimizeButtonText) -- 状态切换后延迟刷新
    else
        print("|cffffe00a<|r|cffff7d0aWChat|r|cffffe00a>|r |cffff0000未找到聊天窗口最小化按钮|r")
    end
end

-- 密语按钮闪烁管理
local whisperButtonFrame, whisperFlashTimer

local function HasUnreadWhispers()
    local db = GetWhisperDB()
    if not (db and db.record and db.record[1]) then return false end
    for _, playerData in pairs(db.record[1]) do
        if playerData and playerData.hasUnread then return true end
    end
    return false
end

local function UpdateWhisperButtonFlash()
    if not whisperButtonFrame then return end
    if whisperFlashTimer then whisperFlashTimer:Cancel(); whisperFlashTimer = nil end

    if HasUnreadWhispers() then
        local isBright = true
        whisperFlashTimer = C_Timer.NewTicker(CONSTANTS.FLASH.INTERVAL, function()
            isBright = not isBright
            whisperButtonFrame:SetAlpha(isBright and 1 or 0.1)
        end)
    end
    whisperButtonFrame:SetAlpha(1)
end

local function WhisperRecord_OnClick()
    if WChat.ToggleWhisperRecord then WChat.ToggleWhisperRecord() end
end

local function Gold_OnClick()
    if not IsAddOnLoaded("BiaoGe") then LoadAddOn("BiaoGe") end
    local bg = _G.BG
    if bg and bg.MainFrame then bg.MainFrame:SetShown(not bg.MainFrame:IsVisible()) end
end

local function AI_OnClick()
    if not IsAddOnLoaded("BiaoGeAI") then LoadAddOn("BiaoGeAI") end
    local ai = _G.BGAI
    if ai and ai.MainFrame then ai.MainFrame:SetShown(not ai.MainFrame:IsVisible()) end
end

local function Atlas_OnClick()
    if not IsAddOnLoaded("AtlasLootClassic") then LoadAddOn("AtlasLootClassic") end
    if SlashCmdList["ATLASLOOT"] then SlashCmdList["ATLASLOOT"]("") end
end

-- 按钮配置列表
local ChannelButtons = {
    {name = "emote", text = "|TInterface/AddOns/WanTiny/Textures/Emotion/excited.tga:14:14:0:0|t", callback = ChatEmote_OnClick},
    {name = "movelock", text = "锁", color = CONSTANTS.COLORS.MOVELOCK, callback = Movelock_OnClick},
    {name = "say", text = "说", color = CONSTANTS.COLORS.SAY, callback = ChannelSay_OnClick},
    {name = "yell", text = "喊", color = CONSTANTS.COLORS.YELL, callback = ChannelYell_OnClick},
    {name = "party", text = "队", color = CONSTANTS.COLORS.PARTY, callback = ChannelParty_OnClick},
    {name = "guild", text = "会", color = CONSTANTS.COLORS.GUILD, callback = ChannelGuild_OnClick},
    {name = "raid", text = "团", color = CONSTANTS.COLORS.RAID, callback = ChannelRaid_OnClick},
    {name = "bg", text = "战", color = CONSTANTS.COLORS.RAID, callback = ChannelBGOrLFG_OnClick},
    {name = "chnGen", text = "综", color = CONSTANTS.COLORS.CHANNEL, callback = ChannelGen_OnClick},
    {name = "chnTrade", text = "交", color = CONSTANTS.COLORS.CHANNEL, callback = ChannelTrade_OnClick},
    {name = "chnLFG", text = "寻", color = CONSTANTS.COLORS.CHANNEL, callback = ChannelLFG_OnClick},
    {name = "world", text = "世", color = CONSTANTS.COLORS.WORLD, callback = ChannelWorld_OnClick},
    {name = "roll", text = "|TInterface/Buttons/UI-GroupLoot-Dice-Up:14:14:0:0|t", color = CONSTANTS.COLORS.ROLL, callback = Roll_OnClick},
    {name = "chatcopy", text = "复", color = CONSTANTS.COLORS.CHATCOPY, callback = ChatCopy_OnClick},
    {name = "minimize", text = "最", color = CONSTANTS.COLORS.GOLD, callback = ChatMinimize_OnClick},
    {name = "whisper", text = "|TInterface/ChatFrame/UI-ChatWhisperIcon:16:16:0:0|t", color = CONSTANTS.COLORS.WHISPER, callback = WhisperRecord_OnClick}
}

-- 动态添加插件按钮
if IsAddOnLoaded("BiaoGe") then table.insert(ChannelButtons, #ChannelButtons-2, {name = "gold", text = "金", color = CONSTANTS.COLORS.GOLD, callback = Gold_OnClick}) end
if IsAddOnLoaded("BiaoGeAI") then table.insert(ChannelButtons, #ChannelButtons-2, {name = "ai", text = "AI", color = CONSTANTS.COLORS.AI, callback = AI_OnClick}) end
if IsAddOnLoaded("AtlasLootClassic") then table.insert(ChannelButtons, #ChannelButtons-2, {name = "atlas", text = "|TInterface/AddOns/WanTiny/Textures/Icon/Atlas_Button.blp:14:14:0:0|t", color = CONSTANTS.COLORS.ATLAS, callback = Atlas_OnClick}) end

-- 按钮创建函数
local function CreateChannelButton(data, index)
    local frame = CreateFrame("Button", data.name, ChatBar)
    frame:SetSize(CONSTANTS.BUTTON.SIZE, CONSTANTS.BUTTON.SIZE)
    frame:SetAlpha(WChat_Config.AlphaOnLeave)
    frame:SetFrameLevel(1)
    frame:RegisterForClicks("AnyUp")
    frame:SetScript("OnClick", data.callback)
    frame:SetScript("OnEnter", function(self)
        self:SetAlpha(WChat_Config.AlphaOnEnter)
        local tooltip = CONSTANTS.TOOLTIPS[data.name]
        if tooltip then
            GameTooltip:SetOwner(self, "ANCHOR_RIGHT")
            GameTooltip:ClearLines()
            for line in string.gmatch(tooltip, "[^\n]+") do
                GameTooltip:AddLine(line, nil, nil, nil, true)
            end
            GameTooltip:Show()
        end
    end)
    frame:SetScript("OnLeave", function(self)
        self:SetAlpha(WChat_Config.AlphaOnLeave)
        if CONSTANTS.TOOLTIPS[data.name] then GameTooltip:Hide() end
    end)

    local frameText = frame:CreateFontString(data.name .. "Text", "ARTWORK")
    frameText:SetFont(STANDARD_TEXT_FONT, CONSTANTS.BUTTON.FONT_SIZE, "OUTLINE")
    frameText:SetJustifyH("CENTER")
    frameText:SetSize(CONSTANTS.BUTTON.TEXT_WIDTH, CONSTANTS.BUTTON.TEXT_HEIGHT)
    frameText:SetText(data.text)
    frameText:SetPoint("CENTER", 0, (type(data.text) == "string" and data.text:find("|T")) and -2 or 0)
    if data.color then frameText:SetTextColor(unpack(data.color)) end
    frame.text = frameText

    if CHANNEL_MAPPINGS[data.name] then
        frame.X = frame:CreateTexture(nil, "OVERLAY")
        frame.X:SetTexture("interface/common/voicechat-muted.blp")
        frame.X:SetSize(14, 14)
        frame.X:SetAlpha(0.7)
        frame.X:SetPoint("CENTER", frame, "CENTER", 0, 0)
        frame.X:SetDrawLayer("OVERLAY", 7)
        frame.X:Hide()
    end

    frame:SetPoint(WChat_Config.UseVertical and "TOP" or "LEFT", ChatBar, WChat_Config.UseVertical and "TOP" or "LEFT",
        WChat_Config.UseVertical and 0 or (10 + (index - 1) * WChat_Config.DistanceHorizontal),
        WChat_Config.UseVertical and ((1 - index) * WChat_Config.DistanceVertical) or 0)
    frame.buttonName = data.name

    if data.name == "bg" then BGButtonFrame = frame; UpdateBGButtonText()
    elseif data.name == "minimize" then WChatBar.MinimizeBtn = frame; C_Timer.After(0.1, UpdateMinimizeButtonText)
    elseif data.name == "whisper" then whisperButtonFrame = frame end
end

function WChat.InitChatBar()
    WChat_Config = GetWChatConfig()
    ChatBar:SetFrameLevel(0)

    -- 设置聊天条尺寸
    local width, height = WChat_Config.UseVertical and 30 or (#ChannelButtons * WChat_Config.DistanceHorizontal + 10),
                         WChat_Config.UseVertical and (#ChannelButtons * WChat_Config.DistanceVertical + 10) or 30
    ChatBar:SetSize(width, height)

    -- 聊天输入框位置调整
    if WChat_Config.UseTopInput then
        DEFAULT_CHAT_FRAME.editBox:ClearAllPoints()
        DEFAULT_CHAT_FRAME.editBox:SetPoint("BOTTOMLEFT", DEFAULT_CHAT_FRAME, "TOPLEFT", 0, 20)
        DEFAULT_CHAT_FRAME.editBox:SetPoint("BOTTOMRIGHT", DEFAULT_CHAT_FRAME, "TOPRIGHT", 0, 20)
    end

    -- 位置设定
    ChatBar:ClearAllPoints()
    if WChat_Config.Position and WChat_Config.Position.point then
        local pos = WChat_Config.Position
        ChatBar:SetPoint(pos.point, pos.relativeTo or UIParent, pos.relativePoint, pos.xOfs, pos.yOfs)
    else
        ChatBar:SetPoint("TOPLEFT", DEFAULT_CHAT_FRAME, "TOPLEFT", 0, 70)
    end

    -- 配置设置
    WChat_Config.AlphaOnLeave, WChat_Config.AlphaOnEnter = WChat_Config.AlphaOnLeave or 0.5, WChat_Config.AlphaOnEnter or 1.0
    ChatBar:SetMovable(true)
    ChatBar:RegisterForDrag("LeftButton")
    ChatBar:SetScript("OnDragStart", ChatBar.StartMoving)
    ChatBar:SetScript("OnDragStop", ChatBar.StopMovingOrSizing)

    -- 创建按钮
    for i = 1, #ChannelButtons do
        CreateChannelButton(ChannelButtons[i], i)
    end

    ChatFrame_RemoveMessageGroup(DEFAULT_CHAT_FRAME, "CHANNEL")

    -- 事件注册
    local events = {"PLAYER_ENTERING_WORLD", "ZONE_CHANGED_NEW_AREA", "UPDATE_CHAT_WINDOWS", "CHAT_MSG_WHISPER", "CHAT_MSG_BN_WHISPER"}
    for _, event in ipairs(events) do ChatBar:RegisterEvent(event) end

    ChatBar:SetScript("OnEvent", function(self, event)
        if event == "PLAYER_ENTERING_WORLD" or event == "ZONE_CHANGED_NEW_AREA" then
            UpdateBGButtonText()
        elseif event == "UPDATE_CHAT_WINDOWS" then
            UpdateChannelXIcons()
        elseif event == "CHAT_MSG_WHISPER" or event == "CHAT_MSG_BN_WHISPER" then
            C_Timer.After(0.1, UpdateWhisperButtonFlash)
        end
    end)

    -- 初始化状态检查
    C_Timer.After(1, function()
        UpdateBGButtonText(); UpdateMinimizeButtonText(); UpdateChannelXIcons()
    end)

    -- 导出函数到全局
    _G.WanTinyDB = _G.WanTinyDB or {}
    _G.WanTinyDB.WChatClassicDB = WChat_Config  -- 兼容性
    _G.WChatClassic = _G.WChatClassic or {}
    _G.WChatClassic.ChatBar = _G.WChatClassic.ChatBar or {}
    _G.WChatClassic.ChatBar.UpdateWhisperButtonFlash = UpdateWhisperButtonFlash

    print("|cffffe00a<|r|cffff7d0aWChat|r|cffffe00a>|r 聊天条加载完毕")
end

-- 聊天复制功能
local chatCopyFrame = CreateFrame("Frame", "ChatCopyFrame", UIParent, "BackdropTemplate")
chatCopyFrame:SetPoint("CENTER", UIParent, "CENTER")
chatCopyFrame:SetSize(700, 400)
chatCopyFrame:Hide()
chatCopyFrame:SetFrameStrata("DIALOG")
chatCopyFrame.close = CreateFrame("Button", nil, chatCopyFrame, "UIPanelCloseButton")
chatCopyFrame.close:SetPoint("TOPRIGHT", chatCopyFrame, "TOPRIGHT")
chatCopyFrame:SetBackdrop({
    bgFile = "Interface/DialogFrame/UI-DialogBox-Background",
    edgeFile = "Interface/DialogFrame/UI-DialogBox-Border",
    tile = true,
    tileSize = 16,
    edgeSize = 16,
    insets = {left = 4, right = 4, top = 4, bottom = 4}
})

local scrollArea = CreateFrame("ScrollFrame", "ChatCopyScrollFrame", chatCopyFrame, "UIPanelScrollFrameTemplate")
scrollArea:SetPoint("TOPLEFT", chatCopyFrame, "TOPLEFT", 10, -30)
scrollArea:SetPoint("BOTTOMRIGHT", chatCopyFrame, "BOTTOMRIGHT", -30, 10)

local editBox = CreateFrame("EditBox", nil, chatCopyFrame)
editBox:SetMultiLine(true)
editBox:SetMaxLetters(99999)
editBox:EnableMouse(true)
editBox:SetAutoFocus(false)
editBox:SetFontObject(ChatFontNormal)
editBox:SetWidth(scrollArea:GetWidth())
editBox:SetHeight(270)
editBox:SetScript("OnEscapePressed", function(f)f:GetParent():GetParent():Hide()f:SetText("") end)
scrollArea:SetScrollChild(editBox)

-- 去除富文本标记按钮
local stripBtn = CreateFrame("Button", nil, chatCopyFrame, "UIPanelButtonTemplate")
stripBtn:SetSize(80, 22)
stripBtn:SetText("纯文本")
stripBtn:SetPoint("BOTTOMRIGHT", chatCopyFrame, "BOTTOMRIGHT", -10, 10)
stripBtn:SetScript("OnClick", function()
    local str = editBox:GetText()
    -- 去除图片、超链接、颜色等富文本标记
    str = str:gsub("|A.-|a", "") -- 图片
    str = str:gsub("|T.-|t", "") -- 图片
    str = str:gsub("|H.-|h(.-)|h", "%1") -- 超链接只保留显示文本
    str = str:gsub("|c%x%x%x%x%x%x%x%x", "") -- 颜色开始
    str = str:gsub("|r", "") -- 颜色结束
    editBox:SetText(str)
end)

function WChat.CopyFunc()
    local cf = SELECTED_CHAT_FRAME
    local lines = {}
    for i = 1, cf:GetNumMessages() do
        local msg = cf:GetMessageInfo(i)
        if msg then
            -- 转换表情图片为占位符（如 [表情] ）
            msg = msg:gsub("|T.-|t", "[表情]")
            -- 也可以自定义为其他字符，如 ":)" 或空格
            table.insert(lines, msg)
        end
    end
    local text = table.concat(lines, "\n")
    chatCopyFrame:Show()
    editBox:SetText(text)
    editBox:HighlightText(0)
end

-- 表情功能
-- 表情选择器框架
local EmoteTableFrame

-- 表情解析规则
local fmtstring

-- 自定义表情开始的序号
local customEmoteStartIndex = 9

local emotes = {
        --原版暴雪提供的8个图标
        {"{rt1}", [=[Interface\TargetingFrame\UI-RaidTargetingIcon_1]=]},
        {"{rt2}", [=[Interface\TargetingFrame\UI-RaidTargetingIcon_2]=]},
        {"{rt3}", [=[Interface\TargetingFrame\UI-RaidTargetingIcon_3]=]},
        {"{rt4}", [=[Interface\TargetingFrame\UI-RaidTargetingIcon_4]=]},
        {"{rt5}", [=[Interface\TargetingFrame\UI-RaidTargetingIcon_5]=]},
        {"{rt6}", [=[Interface\TargetingFrame\UI-RaidTargetingIcon_6]=]},
        {"{rt7}", [=[Interface\TargetingFrame\UI-RaidTargetingIcon_7]=]},
        {"{rt8}", [=[Interface\TargetingFrame\UI-RaidTargetingIcon_8]=]},
        --自定义表情
        {"{天使}", [=[Interface\Addons\WanTiny\Textures\Emotion\Angel]=]},
        {"{生气}", [=[Interface\Addons\WanTiny\Textures\Emotion\Angry]=]},
        {"{大笑}", [=[Interface\Addons\WanTiny\Textures\Emotion\Biglaugh]=]},
        {"{鼓掌}", [=[Interface\Addons\WanTiny\Textures\Emotion\Clap]=]},
        {"{酷}", [=[Interface\Addons\WanTiny\Textures\Emotion\Cool]=]},
        {"{哭}", [=[Interface\Addons\WanTiny\Textures\Emotion\Cry]=]},
        {"{可爱}", [=[Interface\Addons\WanTiny\Textures\Emotion\Cutie]=]},
        {"{鄙视}", [=[Interface\Addons\WanTiny\Textures\Emotion\Despise]=]},
        {"{美梦}", [=[Interface\Addons\WanTiny\Textures\Emotion\Dreamsmile]=]},
        {"{尴尬}", [=[Interface\Addons\WanTiny\Textures\Emotion\Embarrass]=]},
        {"{邪恶}", [=[Interface\Addons\WanTiny\Textures\Emotion\Evil]=]},
        {"{兴奋}", [=[Interface\Addons\WanTiny\Textures\Emotion\Excited]=]},
        {"{晕}", [=[Interface\Addons\WanTiny\Textures\Emotion\Faint]=]},
        {"{打架}", [=[Interface\Addons\WanTiny\Textures\Emotion\Fight]=]},
        {"{流感}", [=[Interface\Addons\WanTiny\Textures\Emotion\Flu]=]},
        {"{呆}", [=[Interface\Addons\WanTiny\Textures\Emotion\Freeze]=]},
        {"{皱眉}", [=[Interface\Addons\WanTiny\Textures\Emotion\Frown]=]},
        {"{致敬}", [=[Interface\Addons\WanTiny\Textures\Emotion\Greet]=]},
        {"{鬼脸}", [=[Interface\Addons\WanTiny\Textures\Emotion\Grimace]=]},
        {"{龇牙}", [=[Interface\Addons\WanTiny\Textures\Emotion\Growl]=]},
        {"{开心}", [=[Interface\Addons\WanTiny\Textures\Emotion\Happy]=]},
        {"{心}", [=[Interface\Addons\WanTiny\Textures\Emotion\Heart]=]},
        {"{恐惧}", [=[Interface\Addons\WanTiny\Textures\Emotion\Horror]=]},
        {"{生病}", [=[Interface\Addons\WanTiny\Textures\Emotion\Ill]=]},
        {"{无辜}", [=[Interface\Addons\WanTiny\Textures\Emotion\Innocent]=]},
        {"{功夫}", [=[Interface\Addons\WanTiny\Textures\Emotion\Kongfu]=]},
        {"{花痴}", [=[Interface\Addons\WanTiny\Textures\Emotion\Love]=]},
        {"{邮件}", [=[Interface\Addons\WanTiny\Textures\Emotion\Mail]=]},
        {"{化妆}", [=[Interface\Addons\WanTiny\Textures\Emotion\Makeup]=]},
        -- {"{马里奥}", [=[Interface\Addons\WanTiny\Textures\Emotion\Mario]=]},
        {"{沉思}", [=[Interface\Addons\WanTiny\Textures\Emotion\Meditate]=]},
        {"{可怜}", [=[Interface\Addons\WanTiny\Textures\Emotion\Miserable]=]},
        {"{好}", [=[Interface\Addons\WanTiny\Textures\Emotion\Okay]=]},
        {"{漂亮}", [=[Interface\Addons\WanTiny\Textures\Emotion\Pretty]=]},
        {"{吐}", [=[Interface\Addons\WanTiny\Textures\Emotion\Puke]=]},
        {"{握手}", [=[Interface\Addons\WanTiny\Textures\Emotion\Shake]=]},
        {"{喊}", [=[Interface\Addons\WanTiny\Textures\Emotion\Shout]=]},
        {"{闭嘴}", [=[Interface\Addons\WanTiny\Textures\Emotion\Shuuuu]=]},
        {"{害羞}", [=[Interface\Addons\WanTiny\Textures\Emotion\Shy]=]},
        {"{睡觉}", [=[Interface\Addons\WanTiny\Textures\Emotion\Sleep]=]},
        {"{微笑}", [=[Interface\Addons\WanTiny\Textures\Emotion\Smile]=]},
        {"{吃惊}", [=[Interface\Addons\WanTiny\Textures\Emotion\Suprise]=]},
        {"{失败}", [=[Interface\Addons\WanTiny\Textures\Emotion\Surrender]=]},
        {"{流汗}", [=[Interface\Addons\WanTiny\Textures\Emotion\Sweat]=]},
        {"{流泪}", [=[Interface\Addons\WanTiny\Textures\Emotion\Tear]=]},
        {"{悲剧}", [=[Interface\Addons\WanTiny\Textures\Emotion\Tears]=]},
        {"{想}", [=[Interface\Addons\WanTiny\Textures\Emotion\Think]=]},
        {"{偷笑}", [=[Interface\Addons\WanTiny\Textures\Emotion\Titter]=]},
        {"{猥琐}", [=[Interface\Addons\WanTiny\Textures\Emotion\Ugly]=]},
        {"{胜利}", [=[Interface\Addons\WanTiny\Textures\Emotion\Victory]=]},
        {"{雷锋}", [=[Interface\Addons\WanTiny\Textures\Emotion\Volunteer]=]},
        {"{委屈}", [=[Interface\Addons\WanTiny\Textures\Emotion\Wronged]=]}
}

-- 自定义聊天过滤器系统
local chatFilters = {}

local function RegisterChatFilter(event, filterFunc)
    if not chatFilters[event] then
        chatFilters[event] = {}
    end
    table.insert(chatFilters[event], filterFunc)
end

local function ChatEmoteFilter(self, event, msg, ...)
    if (WChat_Config and WChat_Config.EnableEmoteInput) then
        for i = customEmoteStartIndex, #emotes do
            if msg:find(emotes[i][1]) then
                msg = msg:gsub(emotes[i][1], format(fmtstring, emotes[i][2]), 1)
            end
        end
    end
    return false, msg, ...
end

local function EmoteIconMouseUp(frame, button)
    if (button == "LeftButton") then
		local chatFrame = GetCVar("chatStyle")=="im" and SELECTED_CHAT_FRAME or DEFAULT_CHAT_FRAME
        local eb = chatFrame and chatFrame.editBox
        if(eb) then
            eb:Insert(frame.text)
            eb:Show();
            eb:SetFocus()
        end
    end
    WChat.ToggleEmoteTable()
end

function WChat.InitEmoteTableFrame()
    WChat_Config = GetWChatConfig()
    fmtstring = format("\124T%%s:%d\124t", max(floor(select(2, SELECTED_CHAT_FRAME:GetFont())), WChat_Config.EmoteIconSize))

    EmoteTableFrame = CreateFrame("Frame", "EmoteTableFrame", UIParent, "BackdropTemplate")

    EmoteTableFrame:SetMovable(true)
    EmoteTableFrame:RegisterForDrag("LeftButton")
    EmoteTableFrame:SetScript("OnDragStart", EmoteTableFrame.StartMoving)
    EmoteTableFrame:SetScript("OnDragStop", EmoteTableFrame.StopMovingOrSizing)
    EmoteTableFrame:EnableMouse(true)

    EmoteTableFrame:SetBackdrop(
        {
            bgFile = "Interface\\Buttons\\WHITE8x8",
            edgeFile = "Interface\\Tooltips\\UI-Tooltip-Border",
            tile = true,
            tileSize = 16,
            edgeSize = 16,
            insets = {left = 3, right = 3, top = 3, bottom = 3}
        }
    )
    EmoteTableFrame:SetBackdropColor(0.05, 0.05, 0.05, 0.8)
    EmoteTableFrame:SetBackdropBorderColor(0.3, 0.3, 0.3)
    EmoteTableFrame:SetWidth((WChat_Config.EmoteIconListSize + 6) * 12 + 10)
    EmoteTableFrame:SetHeight((WChat_Config.EmoteIconListSize + 6) * 5 + 10)
    EmoteTableFrame:SetPoint("BOTTOM", ChatFrame1EditBox, WChat_Config.EmoteOffsetX, WChat_Config.EmoteOffsetY)
    -- 表情选择框出现位置 默认30,30
    EmoteTableFrame:Hide()
    EmoteTableFrame:SetFrameStrata("DIALOG")
    
    local icon, row, col
    row = 1
    col = 1
    for i = 1, #emotes do
        text = emotes[i][1]
        texture = emotes[i][2]
        icon = CreateFrame("Frame", format("IconButton%d", i), EmoteTableFrame)
        icon:SetWidth(WChat_Config.EmoteIconListSize + 6)
        icon:SetHeight(WChat_Config.EmoteIconListSize + 6)
        icon.text = text
        icon.texture = icon:CreateTexture(nil, "ARTWORK")
        icon.texture:SetTexture(texture)
        icon.texture:SetAllPoints(icon)
        icon:Show()
        icon:SetPoint(
            "TOPLEFT",
            5 + (col - 1) * (WChat_Config.EmoteIconListSize + 6),
            -5 - (row - 1) * (WChat_Config.EmoteIconListSize + 6)
        )
        icon:SetScript("OnMouseUp", EmoteIconMouseUp)
        icon:EnableMouse(true)
        col = col + 1
        if (col > 12) then
            row = row + 1
            col = 1
        end
    end

    -- 注册表情过滤器到各个聊天事件
    RegisterChatFilter("CHAT_MSG_CHANNEL", ChatEmoteFilter)-- 公共频道
    RegisterChatFilter("CHAT_MSG_SAY", ChatEmoteFilter)-- 说
    RegisterChatFilter("CHAT_MSG_YELL", ChatEmoteFilter)-- 大喊
    RegisterChatFilter("CHAT_MSG_RAID", ChatEmoteFilter)-- 团队
    RegisterChatFilter("CHAT_MSG_RAID_LEADER", ChatEmoteFilter)-- 团队领袖
    RegisterChatFilter("CHAT_MSG_PARTY", ChatEmoteFilter)-- 队伍
    RegisterChatFilter("CHAT_MSG_PARTY_LEADER", ChatEmoteFilter)-- 队伍领袖
    RegisterChatFilter("CHAT_MSG_GUILD", ChatEmoteFilter)-- 公会

    RegisterChatFilter("CHAT_MSG_AFK", ChatEmoteFilter)-- AFK玩家自动回复
    RegisterChatFilter("CHAT_MSG_DND", ChatEmoteFilter)-- 切勿打扰自动回复

    -- 副本和副本领袖
    RegisterChatFilter("CHAT_MSG_INSTANCE_CHAT", ChatEmoteFilter)
    RegisterChatFilter("CHAT_MSG_INSTANCE_CHAT_LEADER", ChatEmoteFilter)
    -- 解析战网私聊
    RegisterChatFilter("CHAT_MSG_WHISPER", ChatEmoteFilter)
    RegisterChatFilter("CHAT_MSG_WHISPER_INFORM", ChatEmoteFilter)
    RegisterChatFilter("CHAT_MSG_BN_WHISPER", ChatEmoteFilter)
    RegisterChatFilter("CHAT_MSG_BN_WHISPER_INFORM", ChatEmoteFilter)
    -- 解析社区聊天内容
    RegisterChatFilter("CHAT_MSG_COMMUNITIES_CHANNEL", ChatEmoteFilter)
end

function WChat.ToggleEmoteTable()
    if (EmoteTableFrame:IsShown()) then
        EmoteTableFrame:Hide()
    else
        EmoteTableFrame:Show()
    end
end

-- 聊天界面美化功能
-- 频道页签(Tab)美化：隐藏原贴图，字体高亮/描边/染色
local function SkinChatTabs()
    local inherit = GameFontNormalSmall
    local color = RAID_CLASS_COLORS and RAID_CLASS_COLORS[select(2, UnitClass("player"))] or {r=1,g=0.82,b=0}

    -- 频道页签字体颜色由本插件刷新控制

    local function updateFS(tab, inc, flags, ...)
        local fstring = tab:GetFontString()
        if not fstring then return end
        local font, fontSize = inherit:GetFont()
        if inc then
            fstring:SetFont(font, fontSize + 1, flags)
        else
            fstring:SetFont(font, fontSize, flags)
        end
        if select('#', ...) > 0 then
            fstring:SetTextColor(...)
        end
    end
    local function OnEnter(self)
        updateFS(self, nil, "OUTLINE", color.r, color.g, color.b)
    end
    local function OnLeave(self)
        local r, g, b
        local id = self:GetID()
        local emphasis = _G["ChatFrame"..id..'TabFlash']:IsShown()
        if (_G["ChatFrame"..id] == SELECTED_CHAT_FRAME) then
            r, g, b = color.r, color.g, color.b
        elseif emphasis then
            r, g, b = 1, 1, 1
        else
            r, g, b = 1, 1, 1
        end
        updateFS(self, emphasis, nil, r, g, b)
    end
    local function faneifyTab(tab)
        if not tab then return end
        if not tab.Fane then
            if tab.leftTexture then tab.leftTexture:Hide() end
            if tab.middleTexture then tab.middleTexture:Hide() end
            if tab.rightTexture then tab.rightTexture:Hide() end
            if tab.leftSelectedTexture then tab.leftSelectedTexture:Hide(); tab.leftSelectedTexture.Show = tab.leftSelectedTexture.Hide end
            if tab.middleSelectedTexture then tab.middleSelectedTexture:Hide(); tab.middleSelectedTexture.Show = tab.middleSelectedTexture.Hide end
            if tab.rightSelectedTexture then tab.rightSelectedTexture:Hide(); tab.rightSelectedTexture.Show = tab.rightSelectedTexture.Hide end
            if tab.leftHighlightTexture then tab.leftHighlightTexture:Hide() end
            if tab.middleHighlightTexture then tab.middleHighlightTexture:Hide() end
            if tab.rightHighlightTexture then tab.rightHighlightTexture:Hide() end
            tab:HookScript('OnEnter', OnEnter)
            tab:HookScript('OnLeave', OnLeave)
            tab:SetAlpha(1)
            tab.Fane = true
        end
        -- 每次都刷新颜色
        local id = tab:GetID()
        if id == SELECTED_CHAT_FRAME:GetID() then
            updateFS(tab, nil, nil, color.r, color.g, color.b)
        else
            updateFS(tab, nil, nil, 1, 1, 1)
        end
    end
    hooksecurefunc('FCFTab_UpdateColors', faneifyTab)
    for i=1, NUM_CHAT_WINDOWS do
        local tab = _G['ChatFrame'..i..'Tab']
        if tab then faneifyTab(tab) end
    end
end

-- 聊天窗口最小化按钮
local function CreateChatMinimizeButton()
    if _G.WChat_ChatHider then return end
    local ChatHider = CreateFrame("Frame", "WChat_ChatHider", UIParent)
    ChatHider:SetSize(1,1)
    ChatHider:SetFrameStrata("LOW")
    ChatHider:SetPoint("BOTTOMLEFT", UIParent, 0, 0)
    local btn = CreateFrame("Button", "WChat_ChatHideButton", UIParent)
    btn:SetSize(32,36)
    btn:Hide() -- 不显示按钮图标
    -- 保留逻辑，允许外部调用 btn:Click() 实现最小化/还原
    local ChatIsHidden = false
    btn:SetScript("OnClick", function(self, button)
        if not ChatIsHidden then
            for i=1, NUM_CHAT_WINDOWS do _G["ChatFrame"..i]:SetParent(ChatHider) end
            if _G.GeneralDockManager then _G.GeneralDockManager:SetParent(ChatHider) end
            if _G.ChatFrameMenuButton then _G.ChatFrameMenuButton:SetParent(ChatHider) end
            if _G.ChatFrameChannelButton then _G.ChatFrameChannelButton:SetParent(ChatHider) end
            ChatHider:Hide()
            ChatIsHidden = true
        else
            ChatHider:Show()
            ChatIsHidden = false
        end
    end)
end

function WChat.InitChatSkin()
    SkinChatTabs()
    CreateChatMinimizeButton()
end

-- 频道快速切换功能
-- 提取自网易有爱163Chat

function ChatEdit_CustomTabPressed(...)
    return ChatEdit_CustomTabPressed_Inner(...)
end

local cycles = {
        -- "说"
        {
            chatType = "SAY",
            use = function(self, editbox)
                return 1
            end
        },
        --大喊
        {
            chatType = "YELL",
            use = function(self, editbox)
                return 1
            end
        },
        --小队
        {
            chatType = "PARTY",
            use = function(self, editbox)
                return IsInGroup()
            end
        },
        --团队
        {
            chatType = "RAID",
            use = function(self, editbox)
                return IsInRaid()
            end
        },
        --实时聊天
        {
            chatType = "INSTANCE_CHAT",
            use = function(self, editbox)
                return select(2, IsInInstance()) == "pvp"
            end
        },
        --公会
        {
            chatType = "GUILD",
            use = function(self, editbox)
                return IsInGuild()
            end
        },
        --频道
        {
            chatType = "CHANNEL",
            use = function(self, editbox, currChatType)
                local currNum
                if currChatType ~= "CHANNEL" then
                    currNum = IsShiftKeyDown() and 21 or 0
                else
                    currNum = editbox:GetAttribute("channelTarget")
                end
                local h, r, step = currNum + 1, 20, 1
                if IsShiftKeyDown() then
                    h, r, step = currNum - 1, 1, -1
                end
                for i = h, r, step do
                    local channelNum, channelName = GetChannelName(i)
                    if channelNum > 0 and channelName:find("大脚世界频道") then
                        editbox:SetAttribute("channelTarget", i)
                        return true
                    end
                end
            end
        },
        {
            chatType = "SAY",
            use = function(self, editbox)
                return 1
            end
        }
}

local chatTypeBeforeSwitch, tellTargetBeforeSwitch --记录在频道和密语之间切换时的状态
function ChatEdit_CustomTabPressed_Inner(self)
    if strsub(tostring(self:GetText()), 1, 1) == "/" then
        return
    end
    local currChatType = self:GetAttribute("chatType")
    if (IsControlKeyDown()) then
        if (currChatType == "WHISPER" or currChatType == "BN_WHISPER") then
            --记录之前的密语对象，以便后续切回
            self:SetAttribute("chatType", chatTypeBeforeSwitch or "SAY")
            ChatEdit_UpdateHeader(self)
            chatTypeBeforeSwitch = "WHISPER"
            tellTargetBeforeSwitch = self:GetAttribute("tellTarget")
            return --这里和下面不同，这里可以不返回true
        else
            local newTarget, newTargetType = ChatEdit_GetNextTellTarget()
            if tellTargetBeforeSwitch or (newTarget and newTarget ~= "") then
                self:SetAttribute("chatType", tellTargetBeforeSwitch and chatTypeBeforeSwitch or newTargetType)
                self:SetAttribute("tellTarget", tellTargetBeforeSwitch or newTarget)
                ChatEdit_UpdateHeader(self)
                chatTypeBeforeSwitch = currChatType
                tellTargetBeforeSwitch = nil
                return true --这里必须返回true，否则会被暴雪默认的再切换一次密语对象
            end
        end
    end
    
    --对于说然后SHIFT的情况，因为没有return，所以第一层循环会一直遍历到最后的SAY
    for i, curr in ipairs(cycles) do
        if curr.chatType == currChatType then
            local h, r, step = i + 1, #cycles, 1
            if IsShiftKeyDown() then
                h, r, step = i - 1, 1, -1
            end
            if currChatType == "CHANNEL" then
                h = i
            end --频道仍然要测试一下
            for j = h, r, step do
                if cycles[j]:use(self, currChatType) then
                    self:SetAttribute("chatType", cycles[j].chatType)
                    ChatEdit_UpdateHeader(self)
                    return
                end
            end
        end
    end
end

-- 实现聊天过滤器系统
local function SetupChatFilters()
    local function ApplyFilters(self, event, ...)
        if chatFilters[event] then
            local args = {...}
            for _, filterFunc in ipairs(chatFilters[event]) do
                local result, newMsg = filterFunc(self, event, unpack(args))
                if result == false and newMsg then
                    args[1] = newMsg  -- 更新消息内容
                end
            end
            return false, unpack(args)
        end
        return false, ...
    end

    -- 为所有聊天事件注册过滤器
    local chatEvents = {
        "CHAT_MSG_CHANNEL", "CHAT_MSG_SAY", "CHAT_MSG_YELL", "CHAT_MSG_RAID",
        "CHAT_MSG_RAID_LEADER", "CHAT_MSG_PARTY", "CHAT_MSG_PARTY_LEADER",
        "CHAT_MSG_GUILD", "CHAT_MSG_AFK", "CHAT_MSG_DND", "CHAT_MSG_INSTANCE_CHAT",
        "CHAT_MSG_INSTANCE_CHAT_LEADER", "CHAT_MSG_WHISPER", "CHAT_MSG_WHISPER_INFORM",
        "CHAT_MSG_BN_WHISPER", "CHAT_MSG_BN_WHISPER_INFORM", "CHAT_MSG_COMMUNITIES_CHANNEL"
    }

    for _, event in ipairs(chatEvents) do
        ChatFrame_AddMessageEventFilter(event, ApplyFilters)
    end
end

-- 密语记录功能
function WChat.ToggleWhisperRecord()
    -- 简单的密语记录功能实现
    print("|cffffe00a<|r|cffff7d0aWChat|r|cffffe00a>|r 密语记录功能")
end

-- 时间戳功能
local timestampTag = ""
local isTimestampInitialized = false

-- 设置时间戳格式
local function SetTimestampFormat()
    if not WChat_Config.EnableTimestamp then
        -- 如果禁用时间戳，设置为none或恢复原始格式
        if GetCVar("showTimestamps") ~= "none" then
            SetCVar("showTimestamps", "none")
            _G.CHAT_TIMESTAMP_FORMAT = nil
        end
        return
    end

    local color = WChat_Config.TimestampColor or {1.0, 0.5, 0.0}
    local format = WChat_Config.TimestampFormat or "[%H:%M:%S]"

    -- 确保颜色值在有效范围内
    for i = 1, 3 do
        if color[i] < 0 then color[i] = 0 end
        if color[i] > 1 then color[i] = 1 end
    end

    -- 创建带颜色和超链接的时间戳格式
    timestampTag = string.format("|cff%02x%02x%02x|Hwchat_timestamp:-1|h%s|h|r",
        math.floor(color[1] * 255), math.floor(color[2] * 255), math.floor(color[3] * 255), format)

    if GetCVar("showTimestamps") ~= timestampTag then
        SetCVar("showTimestamps", timestampTag)
        _G.CHAT_TIMESTAMP_FORMAT = timestampTag
    end
end

-- 初始化时间戳超链接处理
local function InitTimestampHyperlink()
    if isTimestampInitialized then return end
    isTimestampInitialized = true

    -- Hook ItemRefTooltip的SetHyperlink方法来处理时间戳点击
    local originalSetHyperlink = ItemRefTooltip.SetHyperlink
    function ItemRefTooltip:SetHyperlink(link, ...)
        if link == "wchat_timestamp:-1" then
            local focus = GetMouseFocus()
            if not focus then
                PrintMessage("无法获取鼠标焦点", "ff0000")
                return
            end

            -- 尝试获取FontString对象
            if not focus:IsObjectType("FontString") then
                focus = focus:GetParent()
                if not focus or not focus:IsObjectType("FontString") then
                    PrintMessage("无法找到文本对象", "ff0000")
                    return
                end
            end

            local text = focus:GetText()
            if not text or text == "" then
                PrintMessage("没有找到可复制的文本", "ff0000")
                return
            end

            -- 清理富文本标记，保留纯文本
            local cleanText = text
            cleanText = cleanText:gsub("|T.-|t", "[图标]") -- 替换图标为文本
            cleanText = cleanText:gsub("|H.-|h(.-)|h", "%1") -- 保留超链接显示文本
            cleanText = cleanText:gsub("|c%x%x%x%x%x%x%x%x", "") -- 移除颜色开始标记
            cleanText = cleanText:gsub("|r", "") -- 移除颜色结束标记

            -- 将消息复制到聊天输入框
            local editBox = ChatEdit_ChooseBoxForSend()
            if editBox then
                if not editBox:HasFocus() then
                    ChatEdit_ActivateChat(editBox)
                end
                editBox:SetText(cleanText)
                editBox:HighlightText()
                PrintMessage("消息已复制到输入框")
            else
                PrintMessage("无法找到聊天输入框", "ff0000")
            end
            return
        end
        return originalSetHyperlink(self, link, ...)
    end
end

-- 时间戳颜色选择器
function WChat.ShowTimestampColorPicker()
    if not WChat_Config then
        PrintMessage("WChat配置未初始化", "ff0000")
        return
    end

    if ColorPickerFrame:IsShown() then
        ColorPickerFrame:Hide()
        return
    end

    local color = WChat_Config.TimestampColor or {1.0, 0.5, 0.0}
    local originalColor = {color[1], color[2], color[3]} -- 保存原始颜色用于取消操作

    ColorPickerFrame.func = nil
    ColorPickerFrame.cancelFunc = nil
    ColorPickerFrame:SetColorRGB(color[1], color[2], color[3])

    ColorPickerFrame.func = function()
        local r, g, b = ColorPickerFrame:GetColorRGB()
        WChat_Config.TimestampColor = {r, g, b}
        if WChat_Config.EnableTimestamp then
            SetTimestampFormat()
        end
        PrintMessage("时间戳颜色已更新")
    end

    ColorPickerFrame.cancelFunc = function()
        WChat_Config.TimestampColor = originalColor
        if WChat_Config.EnableTimestamp then
            SetTimestampFormat()
        end
        PrintMessage("颜色选择已取消")
    end

    ColorPickerFrame:Show()
end

-- 设置时间戳格式
function WChat.SetTimestampFormat(format)
    if not WChat_Config then
        PrintMessage("WChat配置未初始化", "ff0000")
        return
    end

    if not format or format == "" then
        PrintMessage("无效的时间戳格式", "ff0000")
        return
    end

    WChat_Config.TimestampFormat = format
    if WChat_Config.EnableTimestamp then
        SetTimestampFormat()
    end
    PrintMessage("时间戳格式已更新为: " .. format)
end

-- 切换时间戳启用状态
function WChat.ToggleTimestamp(enabled)
    if not WChat_Config then
        PrintMessage("WChat配置未初始化", "ff0000")
        return
    end

    WChat_Config.EnableTimestamp = enabled
    if enabled then
        InitTimestampHyperlink()
        SetTimestampFormat()
        PrintMessage("时间戳功能已启用 - 点击时间戳可复制消息")
    else
        SetTimestampFormat()
        PrintMessage("时间戳功能已禁用")
    end
end

-- 主初始化函数
function WChat.Initialize()
    WChat_Config = GetWChatConfig()

    -- 初始化各个功能模块
    WChat.InitChatBar()
    WChat.InitEmoteTableFrame()
    WChat.InitChatSkin()

    -- 设置聊天过滤器
    SetupChatFilters()

    -- 初始化时间戳功能
    if WChat_Config.EnableTimestamp then
        InitTimestampHyperlink()
        SetTimestampFormat()
    end

    -- 导出到全局命名空间以保持兼容性
    _G.WChatClassic = _G.WChatClassic or {}
    _G.WChatClassic.ToggleWhisperRecord = WChat.ToggleWhisperRecord
    _G.WChatClassic.ChatBar = _G.WChatClassic.ChatBar or {}
    _G.WChatClassic.ChatBar.UpdateWhisperButtonFlash = UpdateWhisperButtonFlash

    -- 导出WChat模块到全局，供UI配置使用
    _G.WChat = WChat

    -- 设置配置变更回调
    _G.WChat.OnConfigChanged = {
        EnableTimestamp = function(value)
            WChat.ToggleTimestamp(value)
        end,
        TimestampFormat = function(value)
            WChat.SetTimestampFormat(value)
        end,
        TimestampColor = function(value)
            WChat_Config.TimestampColor = value
            SetTimestampFormat()
        end
    }

    print("|cffffe00a<|r|cffff7d0aWChat|r|cffffe00a>|r 聊天增强模块加载完毕")
end

-- 调用初始化
WChat.Initialize()

end) -- WanTiny_RegisterModule 结束
