-- WChat 时间戳功能快速修复脚本
-- 解决与ALAchat等插件的冲突问题

local function QuickFixWChatTimestamp()
    print("|cffffe00a=== WChat 时间戳快速修复 ===|r")
    
    -- 1. 检查冲突插件
    local hasALAchat = _G.ALAchat ~= nil
    local hasPrat = _G.Prat ~= nil
    local hasChatter = _G.Chatter ~= nil
    
    if hasALAchat then
        print("|cffff8000检测到 ALAchat，启用兼容模式|r")
    end
    if hasPrat then
        print("|cffff8000检测到 Prat|r")
    end
    if hasChatter then
        print("|cffff8000检测到 Chatter|r")
    end
    
    -- 2. 重置时间戳设置
    print("重置时间戳设置...")
    SetCVar("showTimestamps", "none")
    
    -- 3. 等待一秒后重新配置
    C_Timer.After(1, function()
        if not _G.WChat then
            print("|cffff0000错误: WChat未加载|r")
            return
        end
        
        -- 4. 重新初始化WChat时间戳
        print("重新初始化时间戳功能...")
        
        -- 先禁用
        _G.WChat.ToggleTimestamp(false)
        
        -- 再启用
        C_Timer.After(0.5, function()
            _G.WChat.ToggleTimestamp(true)
            
            -- 设置默认颜色和格式
            _G.WChat.OnConfigChanged.TimestampColor({1.0, 0.84, 0.0}) -- 金色
            _G.WChat.SetTimestampFormat("[%H:%M:%S]")
            
            print("|cff00ff00✓ 时间戳功能修复完成！|r")
            print("|cffff8000现在可以点击聊天消息前的时间戳来复制消息|r")
            
            -- 如果有ALAchat，给出特别提示
            if hasALAchat then
                print("|cffff8000注意: 由于ALAchat存在，建议禁用ALAchat的时间戳功能以避免冲突|r")
            end
        end)
    end)
end

-- 禁用ALAchat时间戳功能的函数
local function DisableALAchatTimestamp()
    if not _G.ALAchat then
        print("|cffff0000ALAchat未检测到|r")
        return
    end
    
    print("尝试禁用ALAchat时间戳功能...")
    
    -- 尝试重置ALAchat的时间戳设置
    if _G.ALAchat.db and _G.ALAchat.db.profile then
        local profile = _G.ALAchat.db.profile
        if profile.copy then
            profile.copy.enable = false
            print("|cff00ff00✓ ALAchat时间戳功能已禁用|r")
        end
    end
    
    -- 重置时间戳格式
    SetCVar("showTimestamps", "none")
    
    print("|cffff8000请重新加载界面: /reload|r")
end

-- 完全重置函数
local function CompleteReset()
    print("|cffffe00a=== 完全重置时间戳功能 ===|r")
    
    -- 重置CVar
    SetCVar("showTimestamps", "none")
    
    -- 重置WChat配置
    if WChat_Config then
        WChat_Config.EnableTimestamp = false
        WChat_Config.TimestampColor = {1.0, 0.5, 0.0}
        WChat_Config.TimestampFormat = "[%H:%M:%S]"
        print("✓ WChat配置已重置")
    end
    
    -- 如果有ALAchat，尝试重置其配置
    if _G.ALAchat and _G.ALAchat.db and _G.ALAchat.db.profile and _G.ALAchat.db.profile.copy then
        _G.ALAchat.db.profile.copy.enable = false
        print("✓ ALAchat时间戳已禁用")
    end
    
    print("|cff00ff00完全重置完成，请运行 QuickFixWChatTimestamp() 重新启用|r")
end

-- 暴露函数到全局
_G.QuickFixWChatTimestamp = QuickFixWChatTimestamp
_G.DisableALAchatTimestamp = DisableALAchatTimestamp
_G.CompleteResetTimestamp = CompleteReset

-- 提供使用说明
print("|cffffe00a=== WChat 时间戳修复工具已加载 ===|r")
print("|cff00ff00可用命令:|r")
print("|cffff8000/script QuickFixWChatTimestamp()|r - 快速修复时间戳功能")
print("|cffff8000/script DisableALAchatTimestamp()|r - 禁用ALAchat时间戳")
print("|cffff8000/script CompleteResetTimestamp()|r - 完全重置时间戳功能")

-- 自动运行快速修复
print("\n|cffff8000自动运行快速修复...|r")
QuickFixWChatTimestamp()
