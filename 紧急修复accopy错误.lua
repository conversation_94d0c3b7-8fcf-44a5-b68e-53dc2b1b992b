-- 紧急修复 accopy:-1 错误的脚本
-- 这个脚本会立即停止错误并重新设置正确的时间戳格式

local function EmergencyFixAccopyError()
    print("|cffff0000=== 紧急修复 accopy 错误 ===|r")
    
    -- 1. 立即禁用所有时间戳功能
    print("步骤1: 禁用所有时间戳功能...")
    SetCVar("showTimestamps", "none")
    _G.CHAT_TIMESTAMP_FORMAT = nil
    
    -- 2. 禁用WChat时间戳
    if _G.WChat and WChat_Config then
        WChat_Config.EnableTimestamp = false
        print("✓ WChat时间戳已禁用")
    end
    
    -- 3. 检查并清理可能的ALAchat设置
    if _G.ALAchat and _G.ALAchat.db and _G.ALAchat.db.profile and _G.ALAchat.db.profile.copy then
        _G.ALAchat.db.profile.copy.enable = false
        print("✓ ALAchat时间戳已禁用")
    end
    
    -- 4. 清理任何可能的accopy相关设置
    for name, value in pairs(_G) do
        if type(value) == "string" and value:find("accopy") then
            _G[name] = nil
            print("✓ 清理了包含accopy的全局变量: " .. name)
        end
    end
    
    print("|cff00ff00✓ 紧急修复完成，错误应该已停止|r")
    print("|cffff8000现在可以安全地重新启用WChat时间戳功能|r")
    
    -- 5. 等待2秒后提供重新启用选项
    C_Timer.After(2, function()
        print("\n|cff00ff00可以运行以下命令重新启用时间戳:|r")
        print("|cffff8000/script SafeEnableWChatTimestamp()|r")
    end)
end

-- 安全启用WChat时间戳的函数
local function SafeEnableWChatTimestamp()
    print("|cff00ff00=== 安全启用WChat时间戳 ===|r")
    
    if not _G.WChat then
        print("|cffff0000错误: WChat未加载|r")
        return
    end
    
    if not WChat_Config then
        print("|cffff0000错误: WChat配置未初始化|r")
        return
    end
    
    -- 1. 确保使用安全的配置
    WChat_Config.EnableTimestamp = true
    WChat_Config.TimestampColor = {1.0, 0.84, 0.0} -- 金色
    WChat_Config.TimestampFormat = "[%H:%M:%S]"
    
    print("✓ 配置已设置")
    
    -- 2. 手动设置正确的时间戳格式（使用新的安全链接类型）
    local color = WChat_Config.TimestampColor
    local format = WChat_Config.TimestampFormat
    local safeFormat = string.format("|cff%02x%02x%02x|Hwchat_ts:-1|h%s|h|r", 
        math.floor(color[1] * 255), math.floor(color[2] * 255), math.floor(color[3] * 255), format)
    
    SetCVar("showTimestamps", safeFormat)
    _G.CHAT_TIMESTAMP_FORMAT = safeFormat
    
    print("✓ 时间戳格式已设置: " .. safeFormat)
    
    -- 3. 重新初始化Hook（如果需要）
    if _G.WChat.ToggleTimestamp then
        _G.WChat.ToggleTimestamp(true)
        print("✓ 时间戳功能已启用")
    end
    
    print("|cff00ff00✓ WChat时间戳功能已安全启用！|r")
    print("|cffff8000现在可以点击聊天消息前的时间戳来复制消息|r")
end

-- 检查当前状态的函数
local function CheckCurrentStatus()
    print("|cffffe00a=== 当前时间戳状态 ===|r")
    
    local currentTimestamp = GetCVar("showTimestamps")
    print("当前时间戳CVar: " .. (currentTimestamp or "none"))
    
    if currentTimestamp and currentTimestamp:find("accopy") then
        print("|cffff0000⚠️  发现问题: 仍包含 accopy|r")
        print("|cffff8000建议运行: EmergencyFixAccopyError()|r")
    elseif currentTimestamp and currentTimestamp:find("wchat_ts") then
        print("|cff00ff00✓ 状态良好: 使用安全的 wchat_ts 格式|r")
    else
        print("时间戳状态: " .. (currentTimestamp == "none" and "已禁用" or "其他格式"))
    end
    
    if WChat_Config then
        print("WChat时间戳启用: " .. tostring(WChat_Config.EnableTimestamp))
    end
end

-- 暴露函数到全局
_G.EmergencyFixAccopyError = EmergencyFixAccopyError
_G.SafeEnableWChatTimestamp = SafeEnableWChatTimestamp
_G.CheckTimestampStatus = CheckCurrentStatus

-- 立即运行紧急修复
print("|cffff0000检测到 accopy 错误，立即运行紧急修复...|r")
EmergencyFixAccopyError()

print("\n|cff00ff00可用命令:|r")
print("|cffff8000/script EmergencyFixAccopyError()|r - 紧急修复accopy错误")
print("|cffff8000/script SafeEnableWChatTimestamp()|r - 安全启用时间戳")
print("|cffff8000/script CheckTimestampStatus()|r - 检查当前状态")
