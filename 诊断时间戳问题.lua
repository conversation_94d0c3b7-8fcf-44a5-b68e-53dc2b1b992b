-- 诊断时间戳问题的脚本
-- 用于查找 accopy:-1 错误的根源

local function DiagnoseTimestampIssue()
    print("|cffffe00a=== 时间戳问题诊断 ===|r")
    
    -- 1. 检查当前时间戳设置
    local currentTimestamp = GetCVar("showTimestamps")
    print("当前时间戳CVar: " .. (currentTimestamp or "nil"))
    
    if currentTimestamp and currentTimestamp:find("accopy") then
        print("|cffff0000发现问题: 时间戳格式包含 accopy|r")
        print("问题格式: " .. currentTimestamp)
    elseif currentTimestamp and currentTimestamp:find("wchat_timestamp") then
        print("|cff00ff00时间戳格式正确: 包含 wchat_timestamp|r")
    else
        print("时间戳格式: " .. (currentTimestamp or "none"))
    end
    
    -- 2. 检查全局时间戳格式
    local globalFormat = _G.CHAT_TIMESTAMP_FORMAT
    print("全局时间戳格式: " .. (globalFormat or "nil"))
    
    if globalFormat and globalFormat:find("accopy") then
        print("|cffff0000发现问题: 全局格式包含 accopy|r")
    end
    
    -- 3. 检查WChat配置
    if WChat_Config then
        print("WChat配置:")
        print("  EnableTimestamp: " .. tostring(WChat_Config.EnableTimestamp))
        print("  TimestampFormat: " .. (WChat_Config.TimestampFormat or "nil"))
        if WChat_Config.TimestampColor then
            print("  TimestampColor: " .. table.concat(WChat_Config.TimestampColor, ", "))
        end
    else
        print("|cffff0000WChat_Config 不存在|r")
    end
    
    -- 4. 检查是否有其他插件设置了时间戳
    print("\n--- 检查可能的冲突源 ---")
    
    -- 检查已知的聊天插件
    local chatAddons = {
        "ALAchat", "Prat", "Chatter", "BasicChatMods", "WIM"
    }
    
    for _, addon in ipairs(chatAddons) do
        if _G[addon] then
            print("检测到聊天插件: " .. addon)
            
            -- 特别检查ALAchat
            if addon == "ALAchat" and _G.ALAchat.db then
                local db = _G.ALAchat.db
                if db.profile and db.profile.copy then
                    print("  ALAchat copy设置: " .. tostring(db.profile.copy.enable))
                end
            end
        end
    end
    
    -- 5. 检查Hook状态
    print("\n--- 检查Hook状态 ---")
    print("ChatFrame_OnHyperlinkShow: " .. (ChatFrame_OnHyperlinkShow and "存在" or "不存在"))
    print("ItemRefTooltip.SetHyperlink: " .. (ItemRefTooltip.SetHyperlink and "存在" or "不存在"))
    
    -- 6. 尝试找到设置accopy的代码
    print("\n--- 搜索accopy设置源 ---")
    
    -- 检查是否有其他全局变量包含accopy
    for name, value in pairs(_G) do
        if type(value) == "string" and value:find("accopy") then
            print("发现全局变量包含accopy: " .. name .. " = " .. value)
        elseif type(value) == "table" and value.accopy then
            print("发现全局表包含accopy字段: " .. name)
        end
    end
    
    print("\n=== 诊断完成 ===")
end

-- 立即修复函数
local function ImmediateFix()
    print("|cffffe00a=== 立即修复 accopy 问题 ===|r")
    
    -- 1. 强制重置时间戳
    SetCVar("showTimestamps", "none")
    _G.CHAT_TIMESTAMP_FORMAT = nil
    
    print("✓ 已重置时间戳设置")
    
    -- 2. 等待一秒后重新设置
    C_Timer.After(1, function()
        if _G.WChat and WChat_Config then
            -- 确保使用正确的格式
            WChat_Config.EnableTimestamp = true
            WChat_Config.TimestampColor = {1.0, 0.84, 0.0}
            WChat_Config.TimestampFormat = "[%H:%M:%S]"
            
            -- 手动设置正确的时间戳格式
            local color = WChat_Config.TimestampColor
            local format = WChat_Config.TimestampFormat
            local correctFormat = string.format("|cff%02x%02x%02x|Hwchat_timestamp:-1|h%s|h|r", 
                math.floor(color[1] * 255), math.floor(color[2] * 255), math.floor(color[3] * 255), format)
            
            SetCVar("showTimestamps", correctFormat)
            _G.CHAT_TIMESTAMP_FORMAT = correctFormat
            
            print("|cff00ff00✓ 已设置正确的时间戳格式|r")
            print("新格式: " .. correctFormat)
            
            -- 重新初始化Hook
            if _G.WChat.ToggleTimestamp then
                _G.WChat.ToggleTimestamp(false)
                C_Timer.After(0.5, function()
                    _G.WChat.ToggleTimestamp(true)
                    print("|cff00ff00✓ 时间戳功能已重新初始化|r")
                end)
            end
        else
            print("|cffff0000错误: WChat或配置不可用|r")
        end
    end)
end

-- 暴露函数
_G.DiagnoseTimestampIssue = DiagnoseTimestampIssue
_G.ImmediateFixTimestamp = ImmediateFix

-- 自动运行诊断
print("|cffff8000运行时间戳问题诊断...|r")
DiagnoseTimestampIssue()

print("\n|cff00ff00可用命令:|r")
print("|cffff8000/script DiagnoseTimestampIssue()|r - 重新运行诊断")
print("|cffff8000/script ImmediateFixTimestamp()|r - 立即修复问题")
