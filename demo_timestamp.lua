-- WChat 时间戳功能演示脚本
-- 使用方法：在游戏中运行 /script 然后粘贴以下代码

local function DemoTimestampFeature()
    print("|cffffe00a=== WChat 时间戳功能演示 ===|r")
    
    -- 检查WChat是否加载
    if not _G.WChat then
        print("|cffff0000错误: WChat模块未加载，请先加载WanTiny插件|r")
        return
    end
    
    print("|cff00ff00WChat模块已加载，开始演示...|r")
    
    -- 1. 启用时间戳功能
    print("\n|cffffff001. 启用时间戳功能|r")
    _G.WChat.ToggleTimestamp(true)
    
    -- 等待一秒
    C_Timer.After(1, function()
        -- 2. 设置橙色时间戳
        print("|cffffff002. 设置时间戳为橙色|r")
        _G.WChat.OnConfigChanged.TimestampColor({1.0, 0.5, 0.0})
        
        C_Timer.After(1, function()
            -- 3. 设置绿色时间戳
            print("|cffffff003. 设置时间戳为绿色|r")
            _G.WChat.OnConfigChanged.TimestampColor({0.0, 1.0, 0.0})
            
            C_Timer.After(1, function()
                -- 4. 设置蓝色时间戳
                print("|cffffff004. 设置时间戳为蓝色|r")
                _G.WChat.OnConfigChanged.TimestampColor({0.0, 0.5, 1.0})
                
                C_Timer.After(1, function()
                    -- 5. 测试不同格式
                    print("|cffffff005. 测试简短格式 [HH:MM]|r")
                    _G.WChat.SetTimestampFormat("[%H:%M]")
                    
                    C_Timer.After(2, function()
                        -- 6. 恢复默认格式
                        print("|cffffff006. 恢复默认格式 [HH:MM:SS]|r")
                        _G.WChat.SetTimestampFormat("[%H:%M:%S]")
                        
                        C_Timer.After(1, function()
                            -- 7. 设置为金色
                            print("|cffffff007. 设置时间戳为金色|r")
                            _G.WChat.OnConfigChanged.TimestampColor({1.0, 0.84, 0.0})
                            
                            print("\n|cff00ff00=== 演示完成 ===|r")
                            print("|cffff8000现在你可以：|r")
                            print("|cffff8000- 在聊天窗口发送消息查看时间戳效果|r")
                            print("|cffff8000- 点击时间戳复制消息到输入框|r")
                            print("|cffff8000- 使用 /wantiny 打开配置界面进行更多设置|r")
                        end)
                    end)
                end)
            end)
        end)
    end)
end

-- 快速设置函数
local function QuickSetup()
    if not _G.WChat then
        print("|cffff0000WChat未加载|r")
        return
    end
    
    -- 启用时间戳并设置为金色
    _G.WChat.ToggleTimestamp(true)
    _G.WChat.OnConfigChanged.TimestampColor({1.0, 0.84, 0.0})
    _G.WChat.SetTimestampFormat("[%H:%M:%S]")
    
    print("|cff00ff00时间戳功能已快速设置完成！|r")
    print("|cffff8000点击聊天消息前的时间戳可复制消息|r")
end

-- 提供两种使用方式
print("|cffffe00a选择演示方式：|r")
print("|cff00ff001. 完整演示: |r|cffff8000DemoTimestampFeature()|r")
print("|cff00ff002. 快速设置: |r|cffff8000QuickSetup()|r")

-- 将函数暴露到全局，方便用户调用
_G.DemoTimestampFeature = DemoTimestampFeature
_G.QuickSetup = QuickSetup
