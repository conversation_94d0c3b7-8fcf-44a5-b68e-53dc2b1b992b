# WChat 时间戳功能故障排除指南

## 常见错误及解决方案

### 1. "Unknown link type" 错误

**错误信息：**
```
ItemRefTooltip:SetHyperlink(): Unknown link type
```

**原因：**
- 与其他聊天插件（如ALAchat、Prat、Chatter等）存在冲突
- 超链接处理器被其他插件覆盖

**解决方案：**

#### 方案1：禁用冲突插件
1. 禁用ALAchat、Prat、Chatter等聊天插件
2. 重新加载界面 `/reload`
3. 重新启用WChat时间戳功能

#### 方案2：使用兼容模式
```lua
-- 运行此脚本启用兼容模式
/script QuickFixTimestamp()
```

#### 方案3：手动重置
```lua
-- 重置时间戳设置
/script SetCVar("showTimestamps", "none")
/script _G.WChat.ToggleTimestamp(false)
/script C_Timer.After(1, function() _G.WChat.ToggleTimestamp(true) end)
```

### 2. 时间戳不显示

**可能原因：**
- 时间戳功能未启用
- 配置被其他插件覆盖
- WChat模块未正确加载

**解决步骤：**
1. 检查WChat是否加载：`/script print(_G.WChat and "已加载" or "未加载")`
2. 检查配置：`/script print(WChat_Config.EnableTimestamp and "已启用" or "未启用")`
3. 手动启用：`/script _G.WChat.ToggleTimestamp(true)`
4. 检查CVar：`/script print(GetCVar("showTimestamps"))`

### 3. 点击时间戳无反应

**可能原因：**
- 超链接处理器未正确Hook
- 点击位置不正确
- 聊天输入框被锁定

**解决步骤：**
1. 确保点击的是有颜色的时间戳部分
2. 检查聊天输入框是否可用
3. 尝试重新初始化：
```lua
/script _G.WChat.ToggleTimestamp(false)
/script _G.WChat.ToggleTimestamp(true)
```

### 4. 颜色设置不生效

**可能原因：**
- 颜色值超出范围
- 时间戳功能未启用
- 配置未保存

**解决步骤：**
1. 检查颜色值：`/script print(unpack(WChat_Config.TimestampColor))`
2. 重新设置颜色：`/script _G.WChat.OnConfigChanged.TimestampColor({1, 0.5, 0})`
3. 强制刷新：`/script _G.WChat.SetTimestampFormat(WChat_Config.TimestampFormat)`

## 诊断工具

### 运行完整诊断
```lua
/script LoadAddOn("WanTiny")
/run TestTimestampFeature()
```

### 检查插件冲突
```lua
/script 
local conflicts = {}
if _G.ALAchat then table.insert(conflicts, "ALAchat") end
if _G.Prat then table.insert(conflicts, "Prat") end
if _G.Chatter then table.insert(conflicts, "Chatter") end
print("冲突插件:", table.concat(conflicts, ", "))
```

### 检查Hook状态
```lua
/script print("ChatFrame_OnHyperlinkShow:", ChatFrame_OnHyperlinkShow and "存在" or "不存在")
```

## 兼容性说明

### 已知兼容插件
- WanTiny其他模块
- 大部分界面美化插件
- 大部分功能性插件

### 已知冲突插件
- ALAchat（时间戳功能冲突）
- Prat（聊天增强冲突）
- Chatter（聊天框架冲突）

### 推荐配置
1. 禁用其他聊天增强插件
2. 使用WChat作为主要聊天增强工具
3. 如需使用其他插件，请按优先级顺序加载

## 重置到默认状态

如果遇到严重问题，可以完全重置时间戳功能：

```lua
-- 完全重置脚本
/script 
SetCVar("showTimestamps", "none")
if WChat_Config then
    WChat_Config.EnableTimestamp = false
    WChat_Config.TimestampColor = {1.0, 0.5, 0.0}
    WChat_Config.TimestampFormat = "[%H:%M:%S]"
end
print("时间戳功能已重置，请重新启用")
```

## 获取帮助

如果以上方法都无法解决问题：

1. 运行完整诊断脚本并记录输出
2. 记录具体的错误信息
3. 列出当前使用的所有插件
4. 提供WoW版本信息

## 预防措施

1. **定期备份配置**：WanTinyDB包含所有配置信息
2. **避免同时使用多个聊天插件**
3. **更新插件时先禁用时间戳功能**
4. **使用`/reload`而不是重启游戏来测试更改**
