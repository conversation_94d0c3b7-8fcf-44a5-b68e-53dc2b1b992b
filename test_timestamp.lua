-- 测试时间戳功能的脚本
-- 在游戏中运行 /script LoadAddOn("WanTiny") 然后运行此脚本

local function TestTimestampFeature()
    print("=== WChat 时间戳功能测试 ===")

    -- 检查是否有其他聊天插件冲突
    print("\n--- 检查插件冲突 ---")
    local conflictingAddons = {}
    if _G.ALAchat then
        table.insert(conflictingAddons, "ALAchat")
    end
    if _G.Prat then
        table.insert(conflictingAddons, "Prat")
    end
    if _G.Chatter then
        table.insert(conflictingAddons, "Chatter")
    end

    if #conflictingAddons > 0 then
        print("|cffff8000警告: 检测到可能冲突的聊天插件:|r")
        for _, addon in ipairs(conflictingAddons) do
            print("  - " .. addon)
        end
        print("|cffff8000建议禁用其他聊天插件以避免冲突|r")
    else
        print("✓ 未检测到冲突的聊天插件")
    end

    -- 检查WChat模块是否加载
    if not _G.WChat then
        print("|cffff0000错误: WChat模块未加载|r")
        return
    end

    print("✓ WChat模块已加载")

    -- 检查时间戳相关函数是否存在
    local functions = {
        "ShowTimestampColorPicker",
        "SetTimestampFormat",
        "ToggleTimestamp"
    }

    for _, funcName in ipairs(functions) do
        if _G.WChat[funcName] then
            print("✓ " .. funcName .. " 函数存在")
        else
            print("✗ " .. funcName .. " 函数不存在")
        end
    end

    -- 检查配置回调是否存在
    if _G.WChat.OnConfigChanged then
        print("✓ OnConfigChanged 回调存在")
        for key, callback in pairs(_G.WChat.OnConfigChanged) do
            print("  - " .. key .. " 回调已注册")
        end
    else
        print("✗ OnConfigChanged 回调不存在")
    end

    -- 检查当前时间戳设置
    print("\n--- 当前时间戳状态 ---")
    local currentFormat = GetCVar("showTimestamps")
    print("当前时间戳格式: " .. (currentFormat or "none"))

    -- 检查是否有Hook冲突
    print("\n--- 检查Hook状态 ---")
    if ChatFrame_OnHyperlinkShow then
        print("✓ ChatFrame_OnHyperlinkShow 存在")
    else
        print("✗ ChatFrame_OnHyperlinkShow 不存在")
    end

    -- 安全测试启用时间戳
    print("\n--- 测试启用时间戳 ---")
    local success, err = pcall(function()
        _G.WChat.ToggleTimestamp(true)
    end)

    if success then
        print("✓ 时间戳启用成功")
    else
        print("✗ 时间戳启用失败: " .. (err or "未知错误"))
    end

    -- 安全测试颜色设置
    print("\n--- 测试颜色设置 ---")
    success, err = pcall(function()
        _G.WChat.OnConfigChanged.TimestampColor({0, 1, 0}) -- 绿色
    end)

    if success then
        print("✓ 颜色设置成功")
    else
        print("✗ 颜色设置失败: " .. (err or "未知错误"))
    end

    -- 安全测试格式设置
    print("\n--- 测试格式设置 ---")
    success, err = pcall(function()
        _G.WChat.SetTimestampFormat("[%H:%M]")
    end)

    if success then
        print("✓ 格式设置成功")
    else
        print("✗ 格式设置失败: " .. (err or "未知错误"))
    end

    print("\n=== 测试完成 ===")
    print("|cff00ff00请在聊天窗口中发送消息测试时间戳显示和点击复制功能|r")
    print("|cffff8000如果遇到错误，请尝试禁用其他聊天插件|r")
end

-- 快速修复函数
local function QuickFix()
    print("=== 快速修复时间戳功能 ===")

    -- 重置时间戳设置
    SetCVar("showTimestamps", "none")

    -- 重新初始化WChat
    if _G.WChat and _G.WChat.ToggleTimestamp then
        _G.WChat.ToggleTimestamp(false)
        C_Timer.After(1, function()
            _G.WChat.ToggleTimestamp(true)
            print("✓ 时间戳功能已重新初始化")
        end)
    end
end

-- 运行测试
TestTimestampFeature()

-- 暴露修复函数
_G.QuickFixTimestamp = QuickFix
